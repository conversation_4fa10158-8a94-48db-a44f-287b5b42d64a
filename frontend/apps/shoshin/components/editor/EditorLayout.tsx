"use client"

import { LeftSidebar } from "./LeftSidebar"
import { MainCanvas } from "./MainCanvas"
import { OutermostSidebar } from "./OutermostSidebar"
import { RightSidebar } from "./RightSidebar"
import { SidebarToggleButton } from "./SidebarToggleButton"
import { TopToolbar } from "./TopToolbar"

export function EditorLayout() {
  return (
    <div className="h-full w-full flex flex-col bg-gradient-to-br from-background via-background to-muted/5">
      {/* Top Toolbar */}
      <TopToolbar />

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden relative">
        {/* Outermost Sidebar - Fixed positioned, overlays on top */}
        <OutermostSidebar />

        {/* Content area with left margin to account for outermost sidebar */}
        <div className="flex-1 relative" style={{ marginLeft: '80px' }}>
          {/* Tools/Blocks Sidebar - Positioned absolutely to overlay */}
          <div className="absolute left-0 top-0 bottom-0 z-10">
            <LeftSidebar />
          </div>

          {/* Main Canvas - Takes full width, content positioned behind sidebar */}
          <div className="absolute inset-0 bg-dots">
            <MainCanvas />
          </div>

          {/* Right Sidebar - Positioned absolutely on the right */}
          <div className="absolute right-0 top-0 bottom-0 z-10">
            <RightSidebar />
          </div>
        </div>

        {/* Sidebar Toggle Button - Shows when sidebar is collapsed */}
        <SidebarToggleButton />
      </div>
    </div>
  )
}
